package com.example.base.core

import android.annotation.SuppressLint
import android.content.Context
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient

/**
 * 核心WebView组件，用于加载Cocos Creator游戏
 */
class CocosWebView(context: Context) : WebView(context) {

    init {
        setupWebView()
    }

    /**
     * 配置WebView的基本设置
     */
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        // 启用JavaScript
        settings.javaScriptEnabled = true
        // 启用DOM存储
        settings.domStorageEnabled = true
        // 设置缓存模式
        settings.cacheMode = WebSettings.LOAD_DEFAULT
        // 启用数据库存储
        settings.databaseEnabled = true
        // 启用应用缓存
        settings.setAppCacheEnabled(true)
        // 设置WebView的缓存路径
        settings.setAppCachePath(context.cacheDir.absolutePath)
        // 支持自动加载图片
        settings.loadsImagesAutomatically = true
        // 支持混合内容
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        // 设置默认编码
        settings.defaultTextEncodingName = "UTF-8"
        // 支持多窗口
        settings.setSupportMultipleWindows(true)
        // 支持文件访问
        settings.allowFileAccess = true
        // 支持通过JS打开窗口
        settings.javaScriptCanOpenWindowsAutomatically = true
        // 设置WebView客户端
        webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                // 页面加载完成后的回调
            }
        }
        // 设置WebChromeClient
        webChromeClient = WebChromeClient()
    }

    /**
     * 加载本地HTML资源
     * @param path 本地HTML文件路径
     */
    fun loadLocalHtml(path: String) {
        loadUrl("file://$path")
    }

    /**
     * 加载远程URL
     * @param url 远程URL地址
     */
    fun loadRemoteUrl(url: String) {
        loadUrl(url)
    }

    /**
     * 执行JavaScript代码
     * @param script JavaScript代码
     */
    fun executeJavaScript(script: String) {
        evaluateJavascript(script, null)
    }

    /**
     * 清除WebView缓存
     */
    fun clearWebViewCache() {
        clearCache(true)
        clearHistory()
        clearFormData()
    }
}