package com.example.base.core

import android.content.Context
import android.util.Log
import com.example.resource.ResourceManager

/**
 * Cocos游戏加载器，负责加载和管理Cocos Creator游戏
 */
class CocosGameLoader(
    private val context: Context,
    private val webView: CocosWebView,
    private val resourceManager: ResourceManager
) {
    private val TAG = "CocosGameLoader"
    
    // 远程资源基础URL
    private var remoteBaseUrl: String = ""
    
    // 远程版本信息URL
    private var remoteVersionUrl: String = ""
    
    // 游戏入口文件名
    private var entryFileName: String = "index.html"
    
    /**
     * 初始化游戏加载器
     * @param remoteBaseUrl 远程资源基础URL
     * @param remoteVersionUrl 远程版本信息URL
     * @param entryFileName 游戏入口文件名，默认为index.html
     */
    fun init(
        remoteBaseUrl: String,
        remoteVersionUrl: String,
        entryFileName: String = "index.html"
    ) {
        this.remoteBaseUrl = remoteBaseUrl
        this.remoteVersionUrl = remoteVersionUrl
        this.entryFileName = entryFileName
    }
    
    /**
     * 加载游戏
     * @param forceRemote 是否强制加载远程资源
     * @param callback 加载完成回调
     */
    fun loadGame(forceRemote: Boolean = false, callback: (Boolean) -> Unit) {
        // 检查本地资源是否可用
        val isLocalAvailable = resourceManager.isLocalResourceAvailable(entryFileName)
        
        if (isLocalAvailable && !forceRemote) {
            // 检查远程资源是否有更新
            val hasUpdate = resourceManager.checkForUpdates(remoteVersionUrl)
            
            if (hasUpdate) {
                // 有更新，下载远程资源
                downloadAndLoadRemoteGame(callback)
            } else {
                // 没有更新，直接加载本地资源
                loadLocalGame(callback)
            }
        } else {
            // 本地资源不可用或强制加载远程资源
            downloadAndLoadRemoteGame(callback)
        }
    }
    
    /**
     * 加载本地游戏
     * @param callback 加载完成回调
     */
    private fun loadLocalGame(callback: (Boolean) -> Unit) {
        try {
            val localEntryPath = resourceManager.getLocalEntryFilePath(entryFileName)
            webView.loadLocalHtml(localEntryPath)
            callback(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading local game", e)
            callback(false)
        }
    }
    
    /**
     * 下载并加载远程游戏
     * @param callback 加载完成回调
     */
    private fun downloadAndLoadRemoteGame(callback: (Boolean) -> Unit) {
        // 先加载远程游戏
        webView.loadRemoteUrl("$remoteBaseUrl/$entryFileName")
        
        // 同时下载远程资源到本地
        resourceManager.downloadResources(remoteBaseUrl, remoteVersionUrl) { success ->
            if (success) {
                Log.d(TAG, "Remote resources downloaded successfully")
            } else {
                Log.e(TAG, "Failed to download remote resources")
            }
            callback(true) // 即使下载失败，游戏也已经从远程加载了
        }
    }
    
    /**
     * 重新加载游戏
     */
    fun reloadGame() {
        webView.reload()
    }
    
    /**
     * 清除游戏缓存
     */
    fun clearGameCache() {
        webView.clearWebViewCache()
        resourceManager.clearLocalResources()
    }
    
    /**
     * 执行游戏中的JavaScript代码
     * @param script JavaScript代码
     */
    fun executeGameScript(script: String) {
        webView.executeJavaScript(script)
    }
}