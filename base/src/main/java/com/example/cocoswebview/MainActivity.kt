package com.example.cocoswebview

import android.os.Bundle
import android.widget.Button
import android.widget.FrameLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.base.core.CocosWebView
import com.example.base.core.CocosGameLoader
import com.example.resource.ResourceManager
import com.example.channel.ChannelManager

class MainActivity : AppCompatActivity() {

    private lateinit var webViewContainer: FrameLayout
    private lateinit var btnReload: Button
    private lateinit var btnForceRemote: Button
    private lateinit var btnClearCache: Button
    
    private lateinit var cocosWebView: CocosWebView
    private lateinit var resourceManager: ResourceManager
    private lateinit var channelManager: ChannelManager
    private lateinit var gameLoader: CocosGameLoader
    
    // 渠道配置
    private val channelId = "channel1"
    private val channelName = "测试渠道"
    private val remoteBaseUrl = "https://example.com/cocos-game" // 替换为实际的远程游戏URL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initViews()
        setupWebView()
        setupButtons()
        loadGame()
    }
    
    private fun initViews() {
        webViewContainer = findViewById(R.id.webview_container)
        btnReload = findViewById(R.id.btn_reload)
        btnForceRemote = findViewById(R.id.btn_force_remote)
        btnClearCache = findViewById(R.id.btn_clear_cache)
    }
    
    private fun setupWebView() {
        // 初始化WebView
        cocosWebView = CocosWebView(this)
        webViewContainer.addView(cocosWebView)
        
        // 初始化资源管理器
        resourceManager = ResourceManager(this)
        
        // 初始化渠道管理器
        channelManager = ChannelManager(this)
        channelManager.init(channelId, channelName, remoteBaseUrl)
        
        // 通过渠道管理器获取游戏加载器
        gameLoader = channelManager.getGameLoader(cocosWebView, resourceManager)
    }
    
    private fun setupButtons() {
        // 重新加载按钮
        btnReload.setOnClickListener {
            gameLoader.reloadGame()
            Toast.makeText(this, "正在重新加载游戏", Toast.LENGTH_SHORT).show()
        }
        
        // 强制远程加载按钮
        btnForceRemote.setOnClickListener {
            loadGame(true)
        }
        
        // 清除缓存按钮
        btnClearCache.setOnClickListener {
            gameLoader.clearGameCache()
            Toast.makeText(this, "缓存已清除", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun loadGame(forceRemote: Boolean = false) {
        Toast.makeText(this, "正在加载游戏...", Toast.LENGTH_SHORT).show()
        
        gameLoader.loadGame(forceRemote) { success ->
            runOnUiThread {
                if (success) {
                    Toast.makeText(this, "游戏加载成功 - 渠道: ${channelManager.getChannelName()}", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "游戏加载失败", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    override fun onDestroy() {
        // 清理WebView资源
        webViewContainer.removeAllViews()
        cocosWebView.destroy()
        super.onDestroy()
    }
}