package com.example.channel

import android.content.Context
import com.example.base.core.CocosGameLoader
import com.example.base.core.CocosWebView
import com.example.resource.ResourceManager

/**
 * 渠道商管理类，负责处理不同渠道的特殊逻辑
 */
class ChannelManager(private val context: Context) {

    // 渠道ID
    private var channelId: String = "default"
    
    // 渠道名称
    private var channelName: String = "默认渠道"
    
    // 渠道特定的远程URL
    private var channelRemoteUrl: String = ""
    
    /**
     * 初始化渠道管理器
     * @param channelId 渠道ID
     * @param channelName 渠道名称
     * @param remoteUrl 渠道特定的远程URL
     */
    fun init(channelId: String, channelName: String, remoteUrl: String) {
        this.channelId = channelId
        this.channelName = channelName
        this.channelRemoteUrl = remoteUrl
    }
    
    /**
     * 获取渠道特定的游戏加载器
     * @param webView WebView实例
     * @param resourceManager 资源管理器实例
     * @return 配置好的游戏加载器
     */
    fun getGameLoader(webView: CocosWebView, resourceManager: ResourceManager): CocosGameLoader {
        val gameLoader = CocosGameLoader(context, webView, resourceManager)
        
        // 配置渠道特定的URL
        val baseUrl = "$channelRemoteUrl/$channelId"
        val versionUrl = "$baseUrl/version.json"
        
        gameLoader.init(baseUrl, versionUrl)
        return gameLoader
    }
    
    /**
     * 获取渠道ID
     * @return 渠道ID
     */
    fun getChannelId(): String {
        return channelId
    }
    
    /**
     * 获取渠道名称
     * @return 渠道名称
     */
    fun getChannelName(): String {
        return channelName
    }
    
    /**
     * 获取渠道特定的远程URL
     * @return 远程URL
     */
    fun getChannelRemoteUrl(): String {
        return channelRemoteUrl
    }
    
    /**
     * 获取渠道特定的资源目录
     * @return 资源目录路径
     */
    fun getChannelResourcePath(): String {
        return "$channelId/resources"
    }
}